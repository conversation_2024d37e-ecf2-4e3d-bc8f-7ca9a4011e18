<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="account.ActionableErrors">
        <t t-if="this.sortedActionableErrors">
            <div class="mb-2 rounded-2 overflow-hidden d-grid gap-2">
                <t t-foreach="this.sortedActionableErrors" t-as="error" t-key="error">
                    <t t-set="level" t-value="error_value.level || 'warning'"/>
                    <div t-att-class="`alert alert-${level} m-0 p-1 ps-3`" role="alert">
                        <div t-att-name="error" style="white-space: pre-wrap;">
                            <t t-out="error_value.message"/>
                            <a class="fw-bold"
                               t-if="error_value.action"
                               href="#"
                               t-on-click.prevent="() => this.handleOnClick(error_value)"
                            >
                                <i class="oi oi-arrow-right ms-1"/>
                                <span class="ms-1" t-out="error_value.action_text"/>
                                <i t-if="level === 'danger'" class="fa fa-warning ms-1"/>
                            </a>
                        </div>
                    </div>
                </t>
            </div>
        </t>
    </t>
</templates>
