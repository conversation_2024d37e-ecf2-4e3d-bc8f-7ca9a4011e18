# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing error(s)"
msgstr "%(count)s 項電子發票錯誤"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing info(s)"
msgstr "%(count)s 項電子發票資訊"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing warning(s)"
msgstr "%(count)s 項電子發票警告"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A cancellation of the EDI has been requested."
msgstr "已請求取消 EDI."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A request for cancellation of the EDI has been called off."
msgstr "取消 EDI 的請求已被取消."

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_send
msgid "Account Move Send"
msgstr "賬戶分錄傳送"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"Are you sure you want to cancel this invoice without waiting for the EDI "
"document to be canceled?"
msgstr "確定要取消此發票而不等待 EDI 文件被取消嗎？"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "附件"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "禁用等級"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"根據錯誤嚴重性阻止文件當前操作:\n"
"  * 訊息：文件沒有被阻止，一切正常。\n"
"  * 警告：存在不會阻止當前電子應收憑單(發票)操作成功的錯誤。\n"
"  * 錯誤：有一個錯誤阻止了當前的電子發票操作."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "取消 EDI 取消動作"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "已取消"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr "無法在此紀錄上停用 (%s) 因為並非所有文件都同步"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "程式碼"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "相容的 EDI"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "建立於"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Download"
msgstr "下載"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "EDI Documents"
msgstr "EDI 文件"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "EDI 格式"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "支援此日記帳分錄的 EDI 格式"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
msgid "EDI: Perform web services operations"
msgstr "EDI:執行網上服務操作"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "EDI 禁用等級"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "EDI 內容"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
msgid "Edi Document"
msgstr "EDI文件"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
msgid "Edi Error Count"
msgstr "EDI 錯誤計數"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
msgid "Edi Error Message"
msgstr "Edi 錯誤訊息"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "EDI 格式"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "Edi 顯示放棄取消按鈕"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "EDI顯示取消按鈕"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_force_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_force_cancel_button
msgid "Edi Show Force Cancel Button"
msgstr "Edi 顯示強制取消按鈕"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "要處理的 EDI Web 服務"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "此日記帳分錄的 EDI 文件"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
msgid "Electronic invoicing"
msgstr "電子發票"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "需要電子發票處理"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "電子發票狀態"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "錯誤"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Force Cancel"
msgstr "強制取消"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "格式名稱"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
msgid "How many EDIs are in error for this move?"
msgstr "此分錄有多少 EDI 出現錯誤?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "識別號"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "詳細資訊"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"無效應收付(發票)設定:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "日記賬"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "分錄"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "名稱"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "按格式過帳只能有一個EDI文件"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Process now"
msgstr "立刻執行"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "重新建立日記帳分錄的序列."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "報表動作"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "請求取消EDI"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Retry"
msgstr "重試"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "發送 XML/EDI 憑單"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "發送"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "狀態"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr "此過帳憑證的所有 EDI 的聚合狀態"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr "過帳 edi_format_id 應收帳單時產生的文件 (並且此文件已處理)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr "以下文件已被傳送，無法重新排序：%s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "The invoice will soon be sent to"
msgstr "發票很快會發送至"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr "電子發票操作期間發生的最後一個錯誤的文件."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "此代碼已存在"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
msgid "This document is being sent by another process already. "
msgstr "文件現正由另一處理程序傳送。"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"This invoice was canceled while the EDIs %s still had a pending cancellation"
" request."
msgstr "此發票已被取消，而 EDI %s 仍有待處理的取消請求。"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "待取消"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "待發送"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "警告"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr "您無法編輯以下分錄 %s ,因為已傳送EDI文件。請改為使用\"請求取消EDI\"按鈕。"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr "您無法刪除附件，因此記錄包含了一個發送給政府的EDI文件"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "⇒ See errors"
msgstr "⇒ 查看錯誤訊息"
