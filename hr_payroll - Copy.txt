Payroll
Manage your employee payroll

Category : Payroll
Technical Name : hr_payroll

Created Menus
Payroll/Configuration/Dashboard
Payroll/Configuration/Dashboard/Warnings
Payroll/Configuration/Salary
Payroll/Configuration/Salary/Other Input Types
Payroll/Configuration/Salary/Rule Categories
Payroll/Configuration/Salary/Rule Parameters
Payroll/Configuration/Salary/Rules
Payroll/Configuration/Salary/Structure Types
Payroll/Configuration/Salary/Structures
Payroll/Configuration/Settings
Payroll/Dashboard
Payroll/Employees
Payroll/Employees/Employees
Payroll/Employees/Salary Attachments
Payroll/Employees/Versions
Payroll/Payslips
Payroll/Payslips/Pay Runs
Payroll/Payslips/Payslips
Payroll/Reporting
Payroll/Reporting/Headcount
Payroll/Reporting/Master Report
Payroll/Reporting/Payroll Analysis
Payroll/Reporting/Payslip Lines
Payroll/Reporting/Payslip Work Days Lines
Payroll/Reporting/Work Entries Analysis
Payroll/Work Entries
Created Views
* INHERIT hr.employee.search.inherit (search)
* INHERIT hr.salary.rule.view.list.m2m (list)
* INHERIT hr.user.preferences.view.form.hr.payroll.inherit (form)
* INHERIT hr.version.payroll.list (list)
* INHERIT payroll.hr.employee.view.form (form)
* INHERIT payroll.hr.employee.view.list.inherit (list)
* INHERIT payroll.hr.work.entry.type.view.form.inherit (form)
* INHERIT payroll.hr.work.entry.type.view.form.inherit.contract (form)
* INHERIT res.config.settings.view.form.inherit.hr.payroll (form)
contribution_register (qweb)
hr.payroll.dashboard.warning.form (form)
hr.payroll.dashboard.warning.list (list)
hr.payroll.dashboard.warning.search (search)
hr.payroll.declaration.mixin.form (form)
hr.payroll.declaration.mixin.list (list)
hr.payroll.edit.payslip.lines.wizard.view.form (form)
hr.payroll.employee.declaration.view.list (list)
hr.payroll.employee.declaration.view.search (search)
hr.payroll.headcount.line.search (search)
hr.payroll.headcount.line.view.list (list)
hr.payroll.headcount.view.form (form)
hr.payroll.headcount.view.list (list)
hr.payroll.headcount.view.search (search)
hr.payroll.index.form.view (form)
hr.payroll.master.report.view.form (form)
hr.payroll.master.report.view.list (list)
hr.payroll.payment.report.wizard.form (form)
hr.payroll.report.view.list (list)
hr.payroll.structure.form (form)
hr.payroll.structure.kanban (kanban)
hr.payroll.structure.list (list)
hr.payroll.structure.select (search)
hr.payroll.structure.type.form (form)
hr.payroll.structure.type.list (list)
hr.payroll.structure.type.search (search)
hr.payslip.form (form)
hr.payslip.input.type.kanban.view (kanban)
hr.payslip.input.type.view.form (form)
hr.payslip.input.type.view.list (list)
hr.payslip.input.type.view.search (search)
hr.payslip.kanban (kanban)
hr.payslip.line.form (form)
hr.payslip.line.list (list)
hr.payslip.line.list.register (list)
hr.payslip.line.list.report (list)
hr.payslip.line.pivot (pivot)
hr.payslip.line.search.report (search)
hr.payslip.line.search.view (search)
hr.payslip.line.select (search)
hr.payslip.list (list)
hr.payslip.pivot (pivot)
hr.payslip.run.calendar (calendar)
hr.payslip.run.form (form)
hr.payslip.run.graph (graph)
hr.payslip.run.kanban (kanban)
hr.payslip.run.pivot (pivot)
hr.payslip.run.search (search)
hr.payslip.select (search)
hr.payslip.worked_days.list (list)
hr.payslip.worked_days.pivot (pivot)
hr.payslip.worked_days.search.report (search)
hr.rule.parameter.form (form)
hr.rule.parameter.list (list)
hr.rule.parameter.search (search)
hr.salary.attachment.form (form)
hr.salary.attachment.list (list)
hr.salary.attachment.pivot (pivot)
hr.salary.attachment.search (search)
hr.salary.rule.category.form (form)
hr.salary.rule.category.list (list)
hr.salary.rule.category.select (search)
hr.salary.rule.form (form)
hr.salary.rule.kanban (kanban)
hr.salary.rule.list (list)
hr.salary.rule.select (search)
hr.work.entry.export.employee.list (list)
hr.work.entry.export.mixin.form (form)
hr.work.entry.export.mixin.list (list)
hr.work.entry.report.view.list (list)
hr.work.entry.report.view.pivot (pivot)
hr.work.entry.report.view.search (search)
hr_payroll_note_demo_content (qweb)
payroll.hr.version.view.list.filter.payrun (search)
payroll.hr.version.view.list.payrun (list)
payroll.report.graph (graph)
payroll.report.pivot (pivot)
payroll.report.search (search)
report_light_payslip (qweb)
report_light_payslip_lang (qweb)
report_payslip (qweb)
report_payslip_lang (qweb)
Defined Reports
Contribution Registers
Payslip
Payslip (Light)