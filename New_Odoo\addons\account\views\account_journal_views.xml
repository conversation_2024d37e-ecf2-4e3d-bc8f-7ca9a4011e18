<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_journal_tree" model="ir.ui.view">
            <field name="name">account.journal.list</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <list string="Account Journal">
                    <field name='sequence' widget='handle'/>
                    <field name="name"/>
                    <field name="type"/>
                    <field name="journal_group_ids" widget="many2many_tags" readonly="1" optional="hide"/>
                    <field name="currency_id" groups="base.group_multi_currency" optional="hide"/>
                    <field name="code" optional="show"/>
                    <field name="default_account_id" optional="show"/>
                    <field name="autocheck_on_post" optional="hide"/>
                    <field name="active" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                    <field name="company_id" groups="!base.group_multi_company" column_invisible="True"/>
                </list>
            </field>
        </record>

        <record id="view_account_journal_form" model="ir.ui.view">
            <field name="name">account.journal.form</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Account Journal">
                    <field name="company_id" invisible="1"/>
                    <field name="bank_statements_source" invisible="1"/>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button class="oe_stat_button" type="action"
                                    name="%(action_account_moves_all_a)d" icon="fa-book"
                                    context="{'search_default_journal_id':id}">
                                    <div class="o_stat_info">
                                        <span class="o_stat_text">Journal Entries</span>
                                    </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1><field name="name" placeholder="e.g. Customer Invoices"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="active" invisible="1"/>
                                <field name="type"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                <field name="country_code" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="bank_account" string="Journal Entries">
                                <group>
                                    <group string="Accounting Information">
                                        <field name="default_account_type" invisible="1"/>
                                        <label for="default_account_id" string="Bank Account"
                                               invisible="type != 'bank'" groups="account.group_account_readonly"/>
                                        <label for="default_account_id" string="Journal Account"
                                               invisible="type != 'credit'" groups="account.group_account_readonly"/>
                                        <label for="default_account_id" string="Cash Account"
                                               invisible="type != 'cash'" groups="account.group_account_readonly"/>
                                        <label for="default_account_id" string="Default Income Account"
                                               invisible="type != 'sale'" groups="account.group_account_readonly"/>
                                        <label for="default_account_id" string="Default Expense Account"
                                               invisible="type != 'purchase'" groups="account.group_account_readonly"/>
                                        <label for="default_account_id" string="Default Account"
                                               help="If set, this account is used to automatically balance entries."
                                               invisible="type != 'general'" groups="account.group_account_readonly"/>
                                        <field name="default_account_id" nolabel="1"
                                               invisible="not type"
                                               required="(id and type in ('bank', 'cash', 'credit')) or type in ('sale', 'purchase')"
                                               options="{'no_quick_create': True}"
                                               groups="account.group_account_readonly"/>
                                        <field name="suspense_account_id"
                                               invisible="type not in ('bank', 'cash', 'credit')"
                                               required="type in ('bank', 'cash', 'credit')"
                                               options="{'no_quick_create': True}"
                                               groups="account.group_account_readonly"/>
                                        <field name="profit_account_id" invisible="type not in ('cash', 'bank')" groups="account.group_account_readonly"/>
                                        <field name="loss_account_id" invisible="type not in ('cash', 'bank')" groups="account.group_account_readonly"/>
                                        <field name="refund_sequence" invisible="type not in ['sale', 'purchase']"/>
                                        <field name="payment_sequence" invisible="type not in ('bank', 'cash', 'credit')"/>
                                        <field name="code" placeholder="e.g. INV"/>
                                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                    </group>
                                    <group name="bank_account_number" string="Bank Account Number" invisible="type != 'bank'">
                                        <field name="company_partner_id" invisible="1"/>
                                        <field name="bank_account_id" string="Account Number" context="{'default_partner_id': company_partner_id}"/>
                                        <field name="bank_id" invisible="not bank_account_id"/>
                                        <field name="bank_statements_source" widget="radio" required="type == 'bank'"  groups="account.group_account_basic"/>
                                    </group>
                                    <group name="bank_source" string="Credit Card Setup" invisible="type != 'credit'">
                                        <label for="bank_statements_source" string="Transaction Feeds" invisible="type != 'credit'" groups="account.group_account_readonly"/>
                                        <field name="bank_statements_source" nolabel="1" widget="radio" required="type == 'credit'" groups="account.group_account_basic"/>
                                    </group>
                                </group>
                            </page>
                            <page id="inbound_payment_settings" string="Incoming Payments" name="page_incoming_payments" invisible="type not in ['cash', 'bank', 'credit']">
                                <field name="available_payment_method_ids" invisible="1"/>
                                <field name="inbound_payment_method_line_ids" nolabel="1" context="{'default_payment_type': 'inbound'}">
                                    <list string="Payment Methods" editable="bottom">
                                        <field name="available_payment_method_ids" column_invisible="True"/>
                                        <field name="payment_type" column_invisible="True"/>
                                        <field name="company_id" column_invisible="True"/>
                                        <field name="sequence" widget="handle"/>
                                        <field name="payment_method_id" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="name"/>
                                        <field name="payment_account_id"
                                               placeholder="No payment journal entries"
                                               string="Outstanding Receipts accounts"
                                               options="{'no_quick_create': True}"
                                               groups="account.group_account_readonly"/>
                                    </list>
                                </field>
                            </page>
                            <page id="outbound_payment_settings" string="Outgoing Payments" name="page_outgoing_payments" invisible="type not in ['cash', 'bank', 'credit']">
                                    <field name="outbound_payment_method_line_ids" nolabel="1" context="{'default_payment_type': 'outbound'}">
                                        <list string="Payment Methods" editable="bottom">
                                            <field name="available_payment_method_ids" column_invisible="True"/>
                                            <field name="payment_type" column_invisible="True"/>
                                            <field name="company_id" column_invisible="True"/>
                                            <field name="sequence" widget="handle"/>
                                            <field name="payment_method_id" options="{'no_create': True, 'no_open': True}"/>
                                            <field name="name"/>
                                            <field name="payment_account_id"
                                                   placeholder="No payment journal entries"
                                                   string="Outstanding Payments accounts"
                                                   options="{'no_quick_create': True}"
                                                   groups="account.group_account_readonly"/>
                                        </list>
                                    </field>
                                    <field name="selected_payment_method_codes" invisible="1"/>
                                    <group name="outgoing_payment" />
                            </page>
                            <page name="advanced_settings" string="Advanced Settings">
                                <group>
                                    <group string="Control-Access" groups="account.group_account_manager">
                                        <div class="text-muted" colspan="2">Keep empty for no control</div>
                                        <field name="account_control_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                                        <field name="restrict_mode_hash_table" groups="account.group_account_readonly" invisible="type not in ['sale', 'purchase', 'general']"
                                        />
                                        <field name="autocheck_on_post"/>
                                    </group>
                                    <!-- email alias -->
                                    <group class="oe_read_only" name="group_alias_ro"
                                           string="Create Invoices upon Emails" invisible="type not in ('sale', 'purchase')">
                                       <field name="alias_id"/>
                                    </group>
                                    <group class="oe_read_only" name="group_alias_ro_general"
                                           string="Create Entries upon Emails" invisible="type != 'general'">
                                       <field name="alias_id"/>
                                    </group>
                                    <field name="display_alias_fields" invisible="1"/>
                                    <group name="group_alias_no_domain" string="Create Invoices upon Emails"
                                           invisible="type not in ('sale', 'purchase') or display_alias_fields">
                                        <div class="content-group" colspan="2">
                                            <a type='action' name='%(action_open_settings)d' class="btn btn-link" role="button">
                                                <i class="oi oi-fw o_button_icon oi-arrow-right"/> Configure Alias Domain
                                            </a>
                                        </div>
                                    </group>
                                    <group name="group_alias_no_domain_general" string="Create Entries upon Emails"
                                           invisible="type != 'general' or display_alias_fields">
                                        <div class="content-group" colspan="2">
                                            <a type='action' name='%(action_open_settings)d' class="btn btn-link" role="button">
                                                <i class="oi oi-fw o_button_icon oi-arrow-right"/> Configure Alias Domain
                                            </a>
                                        </div>
                                    </group>
                                    <group class="oe_edit_only" name="group_alias_edit" string="Create Invoices upon Emails"
                                           invisible="type not in ('sale', 'purchase') or not display_alias_fields">
                                        <label string="Email Alias" for="alias_name"/>
                                        <div class="oe_inline" name="edit_alias" style="display: inline;" dir="ltr" >
                                            <field name="alias_name" placeholder="alias" class="oe_inline"/>@
                                            <field name="alias_domain_id" class="oe_inline" placeholder="e.g. mycompany.com"
                                                   options="{'no_create': True, 'no_open': True}"/>
                                        </div>
                                    </group>
                                    <group class="oe_edit_only" name="group_alias_edit_general" string="Create Entries upon Emails"
                                           invisible="type != 'general' or not display_alias_fields">
                                        <label string="Email Alias" for="alias_name"/>
                                        <div class="oe_inline" name="edit_alias" style="display: inline;" dir="ltr" >
                                            <field name="alias_name" placeholder="alias" class="oe_inline"/>@
                                            <field name="alias_domain_id" class="oe_inline" placeholder="e.g. mycompany.com"
                                                   options="{'no_create': True, 'no_open': True}"/>
                                        </div>
                                    </group>
                                    <!-- edi -->
                                    <group name="group_edi_config" string="Electronic Data Interchange" invisible="1"/>
                                    <!-- email alias end -->
                                    <group string="Payment Communications" invisible="type != 'sale'">
                                        <field name="invoice_reference_type"/>
                                        <field name="invoice_reference_model" invisible="invoice_reference_type == 'none'"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                <chatter/>
                </form>
            </field>
        </record>

        <record id="account_journal_view_kanban" model="ir.ui.view">
            <field name="name">account.journal.kanban</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="card" class="row g-0">
                            <div class="col-6">
                                <field class="fw-bolder" name="name"/>
                            </div>
                            <div class="col-6 ">
                                <field class="float-end" name="type"/>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_account_journal_search" model="ir.ui.view">
            <field name="name">account.journal.search</field>
            <field name="model">account.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search string="Search Account Journal">
                    <field name="name" string="Journal" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <filter name="dashboard" string="Favorites" domain="[('show_on_dashboard', '=', True)]"/>
                    <separator/>
                    <filter name="sales" string="Sales" domain="[('type', '=', 'sale')]"/>
                    <filter name="purchases" string="Purchases" domain="[('type', '=', 'purchase')]"/>
                    <filter name="liquidity" string="Liquidity" domain="[('type', 'in', ('cash', 'bank', 'credit'))]"/>
                    <filter name="miscellaneous" string="Miscellaneous" domain="[('type', 'not in', ['sale', 'purchase', 'cash', 'bank', 'credit'])]"/>
                    <separator/>
                    <filter name="inactive" string="Archived" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="action_account_journal_form" model="ir.actions.act_window">
            <field name="name">Journals</field>
            <field name="res_model">account.journal</field>
            <field name="view_mode">list,kanban,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('view_account_journal_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('account_journal_view_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_journal_form')})]"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Add a journal
              </p><p>
                A journal is used to record transactions of all accounting data
                related to the day-to-day business.
              </p>
            </field>
        </record>

        <record id="view_account_journal_group_tree" model="ir.ui.view">
            <field name="name">account.journal.group.list</field>
            <field name="model">account.journal.group</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <list editable="bottom">
                    <field name="company_id" column_invisible="True"/>
                    <field name="sequence"  widget="handle"/>
                    <field name="name" placeholder="e.g. GAAP, IFRS, ..."/>
                    <field name="excluded_journal_ids" widget="many2many_tags" options="{'no_create': True}"/>
                    <field name="company_id" groups="base.group_multi_company" placeholder="All Companies" options="{'no_create': True}"/>
                </list>
            </field>
        </record>

        <record id="view_account_journal_group_form" model="ir.ui.view">
            <field name="name">account.journal.group.form</field>
            <field name="model">account.journal.group</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Multi-ledger">
                    <sheet>
                        <group>
                            <field name="company_id" invisible="1"/>
                            <field name="name" placeholder="e.g. GAAP, IFRS, ..."/>
                            <field name="excluded_journal_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="sequence" groups="base.group_no_one"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_account_journal_group_list" model="ir.actions.act_window">
            <field name="name">Multi-ledger</field>
            <field name="res_model">account.journal.group</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_multi_ledger"/>
                <h2 class="d-md-block">Ledger group allows managing multiple accounting standards.</h2>
                <p>
                    Create as many ledger groups as needed to maintain separate ledgers for local GAAP, IFRS, or fiscal
                    adjustments, ensuring compliance with diverse regulations.
                </p>
            </field>
        </record>

    </data>
</odoo>
