<templates>

    <t t-name="account.DashboardKanbanRecord">
        <article
            t-att-class="getRecordClasses()"
            t-att-data-id="props.canResequence and props.record.id"
            t-att-tabindex="props.record.model.useSampleModel ? -1 : 0"
            t-on-click="onGlobalClick"
            t-on-dragenter.stop.prevent="() => dropzoneState.visible = true"
            t-ref="root">
            <AccountFileUploader t-if="allowDrop" record="props.record">
                <t t-set-slot="default">
                    <UploadDropZone t-props="dropzoneProps"/>
                    <t t-call="{{ templates[this.constructor.KANBAN_CARD_ATTRIBUTE] }}"
                        t-call-context="renderingContext"/>
                </t>
            </AccountFileUploader>
            <t t-else="" t-call="{{ templates[this.constructor.KANBAN_CARD_ATTRIBUTE] }}"
                t-call-context="renderingContext"/>
            <t t-call="{{ this.constructor.menuTemplate }}"/>
        </article>
    </t>

</templates>
