# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_debit_note
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "<span class=\"o_stat_text\">Debit Notes</span>"
msgstr "<span class=\"o_stat_text\">デビットノート</span>"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr "デビットノートウィザードを追加"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Cancel"
msgstr "キャンセル"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_journal__debit_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" debit notes made from this journal"
msgstr "この仕訳帳から作成される顧客請求書とデビッドノートで同じシーケンスを共有したくない場合は、このボックスをチェックして下さい。"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__copy_lines
msgid "Copy Lines"
msgstr "明細をコピー"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__country_code
msgid "Country Code"
msgstr "国コード"

#. module: account_debit_note
#: model:ir.actions.act_window,name:account_debit_note.action_view_account_move_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Create Debit Note"
msgstr "デビットノートを作成"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_uid
msgid "Created by"
msgstr "作成者"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_date
msgid "Created on"
msgstr "作成日"

#. module: account_debit_note
#: model:ir.actions.server,name:account_debit_note.action_move_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_filter_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_line_filter_debit
msgid "Debit Note"
msgstr "デビットノート"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__date
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Debit Note Date"
msgstr "デビットノート日付"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/models/account_move.py:0
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_ids
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_invoice_filter_debit
msgid "Debit Notes"
msgstr "デビットノート"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_journal__debit_sequence
msgid "Dedicated Debit Note Sequence"
msgstr "重複したデビッドノートシーケンス"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__display_name
msgid "Display Name"
msgstr "表示名"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__id
msgid "ID"
msgstr "ID"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__journal_id
msgid "If empty, uses the journal of the journal entry to be debited."
msgstr "空の場合、借方仕訳の仕訳を使用します。"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__copy_lines
msgid ""
"In case you need to do corrections for every line, it can be in handy to "
"copy them.  We won't copy them for debit notes from credit notes. "
msgstr "全項目を修正する必要がある場合は、コピーすれば簡単に使用できます。クレジットノートからデビットノートへのコピーはできません。"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_journal
msgid "Journal"
msgstr "仕訳帳"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_type
msgid "Journal Type"
msgstr "仕訳帳タイプ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_ids
msgid "Move"
msgstr "仕訳"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_type
msgid "Move Type"
msgstr "移動タイプ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_count
msgid "Number of Debit Notes"
msgstr "デビットノートの数"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_origin_id
msgid "Original Invoice Debited"
msgstr "参照元請求書借方記入済"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__reason
msgid "Reason"
msgstr "理由"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISOの国別コードは2文字です。\n"
"これを使ってクイックサーチできます。"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_move__debit_note_ids
msgid "The debit notes created for this invoice"
msgstr "この請求書のために作成されたデビットノート"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "This debit note was created from: %s"
msgstr "デビットノートが以下から作成されました：%s"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_id
msgid "Use Specific Journal"
msgstr "仕訳帳を指定"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can make a debit note only for a Customer Invoice, a Customer Credit "
"Note, a Vendor Bill or a Vendor Credit Note."
msgstr "デビットノートを作成できるのは、顧客請求書、顧客クレジットノート、仕入先請求書、仕入先クレジットノート用のみです。"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "You can only debit posted moves."
msgstr "記帳済仕訳のみ借方記入できます。"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can't make a debit note for an invoice that is already linked to a debit"
" note."
msgstr "既にデビットノートにリンクされている請求書に対してデビットノートを作成することはできません。"
